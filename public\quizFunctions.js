(function(global) {
    // Client-side request tracking to prevent duplicate requests
    const pendingRequests = new Set();

    // Global framework cache to share between components
    if (!window.frameworkCache) {
      window.frameworkCache = {};
    }

    // Global question cache to prevent duplicate API requests
    if (!window.questionCache) {
      window.questionCache = {};
    }

    // Helper function to check if a request is pending
    function isRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      return pendingRequests.has(requestKey);
    }

    // Helper function to mark a request as pending
    function markRequestPending(endpoint, params) {
      const requestKey = `${endpoint}_${JSON.stringify(params)}`;
      pendingRequests.add(requestKey);
      console.log(`Request marked as pending: ${requestKey}`);
      return requestKey;
    }

    // Helper function to mark a request as completed
    function markRequestCompleted(requestKey) {
      pendingRequests.delete(requestKey);
      console.log(`Request marked as completed: ${requestKey}`);
    }

    // Helper function to cache framework data
    function cacheFramework(role, framework) {
      window.frameworkCache[role] = framework;
      console.log(`Framework cached for role: ${role}`);
    }

    // Helper function to get cached framework data
    function getCachedFramework(role) {
      return window.frameworkCache[role];
    }

    // Helper function to cache questions
    function cacheQuestions(role, section, type, questions) {
      const cacheKey = `${role}_${section}_${type}`;
      window.questionCache[cacheKey] = questions;
      console.log(`Cached ${type} questions for ${role}/${section}`);
    }

    // Helper function to get cached questions
    function getCachedQuestions(role, section) {
      const cacheKey = `${role}_${section}_regular`;
      return window.questionCache[cacheKey];
    }

    // Helper function to get cached self-assessment questions
    function getCachedSelfAssessmentQuestions(role, section) {
      const cacheKey = `${role}_${section}_self-assessment`;
      return window.questionCache[cacheKey];
    }

    const startQuiz = () => {
        // Reset all scores and global state variables
        currentQuestion = 0;
        score = 0;
        sectionScores = {
          "Essentials": 0,
          "Intermediate": 0,
          "Advanced": 0,
          "Champions": 0
        };
        questionsPerSection = {
          "Essentials": 0,
          "Intermediate": 0,
          "Advanced": 0,
          "Champions": 0
        };
        isFinalSuccessContainerDisplayed = false;
        isFailureContainerDisplayed = false;

        document.getElementById("start-page").style.display = "none";
        document.getElementById("consent-popup").style.display = "block";
      };


const endQuiz = async () => {
  const finalResults = window.quizLogger ? window.quizLogger.logFinalResults() : null;
  document.getElementById("quiz-container").style.display = "none";

  // Calculate score based only on knowledge-check questions
  const knowledgeCheckQuestions = quizData.filter(q => q.type === "knowledge-check");
  const totalKnowledgeQuestions = knowledgeCheckQuestions.length;
  const passThreshold = 0.7 * totalKnowledgeQuestions;

  if (score >= passThreshold) {
    if (currentSection === 4) {
        const email = document.getElementById("email").value.trim();
        if (email) {
            try {
                await db.collection('assessmentStatus').doc(email).update({
                    status: 'completed',
                    timestamp: firebase.firestore.FieldValue.serverTimestamp()
                });
            } catch (error) {
                console.error('Error updating assessment status to Firestore:', error);
                showNotification('Error updating assessment status. Please try again.', 'error');
            }
        }
        isFinalSuccessContainerDisplayed = true;
        document.getElementById("final-success-container").style.display = "block";
    } else {
        document.getElementById("success-container").style.display = "block";
        document.getElementById("current-section").innerText = currentSection;
        const successHeading = document.getElementById("success-heading");
        successHeading.innerText = `You passed Section ${currentSection}: ${sectionNames[currentSection - 1]}`;
    }
  } else {
    showLoadingOverlay();
    document.getElementById("failure-container").style.display = "block";
    isFailureContainerDisplayed = true;

    const email = document.getElementById("email").value.trim();

    if (email) {
        try {
            const firstName = document.getElementById("first-name").value.trim();
            const lastName = document.getElementById("last-name").value.trim();
            const phone = document.getElementById("phone").value.trim();
            const role = document.getElementById("role").value.trim();

            // Create the assessment summary object
            const assessmentSummary = {
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                totalScore: score,
                totalKnowledgeQuestions: totalKnowledgeQuestions,
                sectionScores: { ...sectionScores },
                questionsPerSection: { ...questionsPerSection },
                currentSection: currentSection,
                status: 'completed'
            };

            const resultData = {
                employeeEmail: email,
                section: sectionNames[currentSection - 1],
                score,
                role,
                totalQuestions: totalKnowledgeQuestions, // Only count knowledge questions for scoring
                isNewUser: false,
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                firstName,
                lastName,
                userCompany: userCompany,
                userPhone: phone,
            };

            const companyRef = db.collection('companies').doc(userCompany);
            const userRef = companyRef.collection('users').doc(email);
            const assessmentResultsRef = userRef.collection('assessmentResults');

            // Save the assessment summary to a new collection
            await userRef.collection('assessmentSummaries').add(assessmentSummary);
            console.log('Assessment summary saved to Firestore:', assessmentSummary);

            // Add the new assessment result
            await assessmentResultsRef.add(resultData);
            console.log('Assessment result saved to Firestore:', resultData);

            // Update the assessment status under the user document
            await userRef.update({ status: 'completed' });

            hideLoadingOverlay();
            showFeedbackPrompt();
            initializePathwayButton();
            pollForRecommendations(email, userCompany);

            showNotification('Your results are being processed. You will be notified when complete.', 'success');

            sendAssessmentResult(email).then(() => {
                console.log('Assessment result sent for recommendations');
                showNotification('Your results have been processed successfully!', 'success');
            }).catch(error => {
                console.error('Error sending assessment result for recommendations:', error);
                showNotification('An error occurred while processing your results. Please contact support.', 'error');
            });

        } catch (error) {
            console.error('Error saving assessment data to Firestore:', error);
            hideLoadingOverlay();
            showNotification('An error occurred while submitting assessment data. Please try again or contact support.', 'error');
        }
    } else {
        console.log('Email is not provided, assessment data will not be saved.');
        hideLoadingOverlay();
    }
  }
};

      const logFinalResults = () => {
        const currentSectionName = sectionNames[currentSection - 1];

        // Filter responses to only include current section
        const currentSectionResponses = sessionLogs.userResponses.filter(response =>
            response.section === currentSectionName
        );

        const finalResults = {
            timestamp: new Date().toISOString(),
            userInfo: {
                email: document.getElementById("email").value.trim(),
                firstName: document.getElementById("first-name").value.trim(),
                lastName: document.getElementById("last-name").value.trim(),
                role: document.getElementById("role").value.trim(),
                company: userCompany
            },
            quizResults: {
                totalScore: score,
                sectionScores: { [currentSectionName]: sectionScores[currentSectionName] },
                questionsPerSection: { [currentSectionName]: questionsPerSection[currentSectionName] },
                currentSection: currentSection,
                sectionName: currentSectionName,
                totalQuestions: quizData.length,
                passThreshold: 0.7 * quizData.length,
                passed: score >= (0.7 * quizData.length)
            },
            completeSessionData: {
                framework: sessionLogs.framework,
                quiz: sessionLogs.quiz,
                userResponses: currentSectionResponses,
                finalTimestamp: new Date().toISOString()
            }
        };

        console.log('Final Quiz Results:', JSON.stringify(finalResults, null, 2));
        return finalResults;
    };

// Global variables for batch management
let batchedQuestions = [];
let loadedBatches = new Set();
let totalBatches = 0;
let batchSize = 4;
let isProgressiveLoading = false;

// Fixed quiz totals (do not depend on when batches arrive)
const EXPECTED_KNOWLEDGE_TOTAL = 10;
const EXPECTED_SELF_ASSESSMENT_TOTAL = 5;
let FIXED_TOTAL_QUESTIONS = EXPECTED_KNOWLEDGE_TOTAL + EXPECTED_SELF_ASSESSMENT_TOTAL; // 15 by default

// Loading state management
const LoadingState = {
  IDLE: 'idle',
  LOADING_FRAMEWORK: 'loading_framework',
  LOADING_FIRST_BATCH: 'loading_first_batch',
  LOADING_SELF_ASSESSMENT: 'loading_self_assessment',
  LOADING_ADDITIONAL_BATCHES: 'loading_additional_batches',
  COMPLETE: 'complete',
  ERROR: 'error'
};

let currentLoadingState = LoadingState.IDLE;
let loadingErrors = [];
let batchLoadingProgress = {
  total: 0,
  completed: 0,
  failed: 0,
  inProgress: 0
};

// Progressive loading messages configuration
const LoadingMessages = {
  [LoadingState.LOADING_FRAMEWORK]: {
    main: "Personalising your assessment...",
    sub: "Preparing your framework"
  },
  [LoadingState.LOADING_FIRST_BATCH]: {
    main: "Updating your questions...",
    sub: "Generating your first set"
  },
  [LoadingState.LOADING_SELF_ASSESSMENT]: {
    main: "Just a moment...",
    sub: "Creating self-assessment questions"
  },
  [LoadingState.LOADING_ADDITIONAL_BATCHES]: {
    main: "Finalising...",
    sub: "Loading additional questions"
  },
  [LoadingState.COMPLETE]: {
    main: "Assessment ready!",
    sub: "Let's begin your journey"
  },
  [LoadingState.ERROR]: {
    main: "Something went wrong",
    sub: "Please try again"
  }
};

// Function to update loading state and provide user feedback
function updateLoadingState(newState, details = {}) {
  const previousState = currentLoadingState;
  currentLoadingState = newState;

  console.log(`Loading state changed: ${previousState} -> ${newState}`, details);

  const loadingMessage = document.getElementById('quiz-loading-message');
  const loadingSubmessage = document.getElementById('quiz-loading-submessage');

  if (!loadingMessage) return;

  // Get message configuration
  let messageConfig = LoadingMessages[newState];

  // Handle special cases
  if (newState === LoadingState.LOADING_ADDITIONAL_BATCHES) {
    const { completed, total } = batchLoadingProgress;
    messageConfig = {
      main: "Finalising...",
      sub: `Batch ${completed}/${total} ready`
    };
  } else if (newState === LoadingState.ERROR && details.message) {
    messageConfig = {
      main: "Something went wrong",
      sub: details.message
    };
  }

  // Update messages with smooth transitions
  updateLoadingMessage(loadingMessage, messageConfig.main);
  if (loadingSubmessage) {
    updateLoadingMessage(loadingSubmessage, messageConfig.sub, true);
  }

  // Update loading steps
  updateLoadingSteps(newState);
}

// Function to smoothly transition loading messages
function updateLoadingMessage(element, newText, isSubmessage = false) {
  if (!element) return;

  // Remove active class to trigger fade out
  element.classList.remove('active');

  // Wait for fade out, then update text and fade in
  setTimeout(() => {
    element.textContent = newText;
    element.classList.add('active');
  }, isSubmessage ? 200 : 150);
}

// Function to update loading steps indicator
function updateLoadingSteps(state) {
  const steps = document.querySelectorAll('.step');
  if (!steps.length) return;

  // Reset all steps
  steps.forEach(step => {
    step.classList.remove('active', 'completed');
  });

  let activeStep = 1;
  let completedSteps = 0;

  switch (state) {
    case LoadingState.LOADING_FRAMEWORK:
      activeStep = 1;
      break;
    case LoadingState.LOADING_FIRST_BATCH:
    case LoadingState.LOADING_SELF_ASSESSMENT:
    case LoadingState.LOADING_ADDITIONAL_BATCHES:
      activeStep = 2;
      completedSteps = 1;
      break;
    case LoadingState.COMPLETE:
      activeStep = 3;
      completedSteps = 2;
      break;
    case LoadingState.ERROR:
      // Keep current state for errors
      return;
  }

  // Mark completed steps
  for (let i = 0; i < completedSteps; i++) {
    if (steps[i]) {
      steps[i].classList.add('completed');
    }
  }

  // Mark active step
  if (steps[activeStep - 1]) {
    steps[activeStep - 1].classList.add('active');
  }
}

// Function to handle loading errors gracefully
function handleLoadingError(error, context = '') {
  console.error(`Loading error in ${context}:`, error);
  loadingErrors.push({ error: error.message, context, timestamp: Date.now() });

  // Show user-friendly error message
  const loadingMessage = document.getElementById('quiz-loading-message');
  if (loadingMessage) {
    loadingMessage.textContent = `Having trouble loading questions. Retrying...`;
  }

  // Update progress to show we're handling the error
  updateLoadingProgress(Math.max(currentProgress - 10, 10));
}

// Function to update batch loading progress
function updateBatchProgress(batchNumber, status) {
  switch (status) {
    case 'started':
      batchLoadingProgress.inProgress++;
      break;
    case 'completed':
      batchLoadingProgress.completed++;
      batchLoadingProgress.inProgress = Math.max(0, batchLoadingProgress.inProgress - 1);
      break;
    case 'failed':
      batchLoadingProgress.failed++;
      batchLoadingProgress.inProgress = Math.max(0, batchLoadingProgress.inProgress - 1);
      break;
  }

  // Update UI batch progress indicator
  if (window.updateBatchProgress && batchLoadingProgress.total > 0) {
    window.updateBatchProgress(
      batchLoadingProgress.completed,
      batchLoadingProgress.total,
      batchNumber
    );
  }

  // Update loading state if we're in the additional batches loading state
  if (currentLoadingState === LoadingState.LOADING_ADDITIONAL_BATCHES) {
    updateLoadingState(LoadingState.LOADING_ADDITIONAL_BATCHES);
  }

  console.log(`Batch ${batchNumber} ${status}. Progress:`, batchLoadingProgress);
}

// Progressive loading function - loads questions in batches
const loadQuizDataProgressive = async () => {
  showQuizLoadingOverlay();
  isProgressiveLoading = true;

  // Start performance tracking
  PerformanceTracker.start();

  // Reset loading state
  currentLoadingState = LoadingState.IDLE;
  loadingErrors = [];
  batchLoadingProgress = { total: 0, completed: 0, failed: 0, inProgress: 0 };

  let currentProgress = 0;
  updateLoadingProgress(currentProgress);

  try {
    updateLoadingState(LoadingState.LOADING_FRAMEWORK);

    const role = document.getElementById("role").value.trim();
    const currentSectionName = sectionNames[currentSection - 1];
    const email = document.getElementById("email")?.value.trim() || null;

    // Get framework
    let framework = getCachedFramework(role);
    if (!framework) {
      console.log('Framework not found in cache, fetching from Firestore');
      const frameworkDoc = await db.collection('frameworks').doc(role).get();
      if (!frameworkDoc.exists) {
        throw new Error('Framework not found for role');
      }
      framework = frameworkDoc.data();
      cacheFramework(role, framework);
    }

    // Calculate total batches needed
    totalBatches = Math.ceil(10 / batchSize);
    batchLoadingProgress.total = totalBatches;
    console.log(`Starting progressive loading with ${totalBatches} batches of ${batchSize} questions each`);

    updateLoadingState(LoadingState.LOADING_FIRST_BATCH);

    // Start loading first batch and self-assessment questions in parallel
    PerformanceTracker.markBatchStart(1);
    const firstBatchPromise = loadQuestionBatch(role, currentSectionName, framework, email, 1)
      .then(batch => {
        PerformanceTracker.markBatchEnd(1);
        return batch;
      })
      .catch(error => {
        handleLoadingError(error, 'first batch');
        throw error;
      });

    updateLoadingState(LoadingState.LOADING_SELF_ASSESSMENT);
    const selfAssessmentPromise = loadSelfAssessmentQuestions(role, currentSectionName, framework, email)
      .catch(error => {
        handleLoadingError(error, 'self-assessment');
        throw error;
      });

    // Wait for first batch to complete
    const firstBatch = await firstBatchPromise;
    updateBatchProgress(1, 'completed');

    // Update progress with smooth animation (now synced with real state)
    if (window.takeOverFromVisualProgress) {
      window.takeOverFromVisualProgress();
    }
    currentProgress = Math.max(currentProgress, 20);
    if (window.animateProgressRing) {
      window.animateProgressRing(currentProgress, 600);
    } else {
      updateLoadingProgress(currentProgress);
    }

    // Initialize quiz with first batch
    batchedQuestions = [...firstBatch];
    loadedBatches.add(1);

    console.log('First batch loaded, displaying initial questions');

    updateLoadingState(LoadingState.LOADING_ADDITIONAL_BATCHES);

    // Start loading remaining batches concurrently in background
    const remainingBatchPromises = [];
    const batchLoadingStatus = new Map(); // Track individual batch status

    for (let i = 2; i <= totalBatches; i++) {
      batchLoadingStatus.set(i, 'pending');
      updateBatchProgress(i, 'started');
      PerformanceTracker.markBatchStart(i);

      const batchPromise = loadQuestionBatch(role, currentSectionName, framework, email, i)
        .then(batch => {
          PerformanceTracker.markBatchEnd(i);
          batchedQuestions.push(...batch);
          loadedBatches.add(i);
          batchLoadingStatus.set(i, 'completed');
          updateBatchProgress(i, 'completed');
          console.log(`Batch ${i} loaded in background (${loadedBatches.size}/${totalBatches} total)`);

          // Update progress as batches complete with smooth animation
          const progressIncrement = (60 - 30) / (totalBatches - 1);
          const newProgress = Math.min(60, 30 + (loadedBatches.size - 1) * progressIncrement);
          if (window.animateProgressRing) {
            window.animateProgressRing(Math.round(newProgress), 400);
          } else {
            updateLoadingProgress(Math.round(newProgress));
          }
          currentProgress = Math.max(currentProgress, newProgress);

          return { batchNumber: i, questions: batch, status: 'success' };
        })
        .catch(error => {
          batchLoadingStatus.set(i, 'failed');
          updateBatchProgress(i, 'failed');
          handleLoadingError(error, `batch ${i}`);
          return { batchNumber: i, questions: [], status: 'error', error };
        });

      remainingBatchPromises.push(batchPromise);
    }

    // Start all batch requests concurrently (non-blocking)
    console.log(`Starting concurrent loading of ${remainingBatchPromises.length} batches`);

    // Use Promise.allSettled to handle partial failures gracefully
    const batchResults = Promise.allSettled(remainingBatchPromises).then(results => {
      const successful = results.filter(r => r.status === 'fulfilled' && r.value.status === 'success').length;
      const failed = results.filter(r => r.status === 'rejected' || (r.status === 'fulfilled' && r.value.status === 'error')).length;

      console.log(`Batch loading complete: ${successful} successful, ${failed} failed`);

      if (failed > 0) {
        console.warn('Some batches failed to load, but quiz can continue with available questions');
      }

      return results;
    });

    // Wait for self-assessment questions
    const selfAssessmentQuestions = await selfAssessmentPromise;
    currentProgress = Math.max(currentProgress, 70);
    if (window.animateProgressRing) {
      window.animateProgressRing(currentProgress, 500);
    } else {
      updateLoadingProgress(currentProgress);
    }

    // Mark self-assessment questions with type
    const typedSelfAssessmentQuestions = selfAssessmentQuestions.map(q => ({
      ...q,
      type: "self-assessment"
    }));

    // Display the quiz with first batch while others load
    setTimeout(async () => {
      // Combine first batch with self-assessment questions
      const typedRegularQuestions = batchedQuestions.map(q => ({
        ...q,
        type: "knowledge-check"
      }));

      // Create initial quiz data with first batch
      quizData = [...typedRegularQuestions, ...typedSelfAssessmentQuestions];
      quizData = shuffleArray(quizData);

      // Cache the first batch and self-assessment questions
      cacheBatchedQuestions(role, currentSectionName, 1, batchedQuestions);
      cacheQuestions(role, currentSectionName, 'self-assessment', selfAssessmentQuestions);

      currentProgress = 100;
      if (window.animateProgressRing) {
        window.animateProgressRing(currentProgress, 500);
      } else {
        updateLoadingProgress(currentProgress);
      }

      // Hide loading and show quiz
      setTimeout(() => {
        updateLoadingState(LoadingState.COMPLETE);
        hideQuizLoadingOverlay();
        loadQuestionEnhanced(); // Use enhanced loading
        updateProgressBar();

        // Mark first question displayed for performance tracking
        PerformanceTracker.markFirstQuestionDisplayed();

        console.log('Progressive quiz loading complete - first batch ready');
      }, 500);

      // Continue loading remaining batches in background
      batchResults.then((results) => {
        console.log('All batch requests completed');

        // Update quiz data with all successfully loaded questions
        const allTypedRegularQuestions = batchedQuestions.map(q => ({
          ...q,
          type: "knowledge-check"
        }));

        // Only re-shuffle if we have more questions than initially
        if (allTypedRegularQuestions.length > quizData.filter(q => q.type === "knowledge-check").length) {
          const allQuizData = [...allTypedRegularQuestions, ...typedSelfAssessmentQuestions];
          const currentQuestionData = quizData[currentQuestion]; // Save current question

          quizData = shuffleArray(allQuizData);

          // If we're still on the same question, keep the position
          if (currentQuestionData) {
            const newIndex = quizData.findIndex(q =>
              q.question === currentQuestionData.question &&
              q.type === currentQuestionData.type
            );
            if (newIndex !== -1 && newIndex !== currentQuestion) {
              // Swap to maintain current question position
              [quizData[currentQuestion], quizData[newIndex]] = [quizData[newIndex], quizData[currentQuestion]];
            }
          }

          console.log(`Quiz data updated with ${allTypedRegularQuestions.length} total questions`);
        }

        // Cache all successfully loaded batches
        const successfulBatches = results.filter(r =>
          r.status === 'fulfilled' && r.value.status === 'success'
        );

        successfulBatches.forEach(result => {
          const { batchNumber, questions } = result.value;
          cacheBatchedQuestions(role, currentSectionName, batchNumber, questions);
        });

        // Cache the combined regular questions
        cacheQuestions(role, currentSectionName, 'regular', allTypedRegularQuestions);

        console.log(`Cached ${successfulBatches.length} batches successfully`);

        // Calculate final performance metrics and optimize for next time
        const metrics = PerformanceTracker.calculateMetrics();
        optimizeBatchSize();

        // Log performance summary
        console.log('Progressive Loading Performance Summary:', {
          totalTime: `${metrics.totalLoadTime.toFixed(2)}ms`,
          timeToFirstQuestion: `${metrics.timeToFirstQuestion.toFixed(2)}ms`,
          averageBatchTime: `${metrics.averageBatchTime.toFixed(2)}ms`,
          errorRate: `${metrics.errorRate.toFixed(1)}%`,
          batchSize: batchSize,
          successfulBatches: successfulBatches.length,
          totalBatches: totalBatches
        });

      }).catch(error => {
        console.error('Error in batch processing:', error);
      });

    }, 500);

  } catch (error) {
    console.error('Error in progressive loading:', error);
    updateLoadingState(LoadingState.ERROR, { message: 'Failed to load questions. Trying alternative method...' });
    handleLoadingError(error, 'progressive loading');
    isProgressiveLoading = false;

    // Show user-friendly error message
    const loadingMessage = document.getElementById('quiz-loading-message');
    if (loadingMessage) {
      loadingMessage.textContent = 'Switching to standard loading method...';
    }

    // Wait a moment before fallback
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Fallback to original loading method
    try {
      await loadQuizDataOriginal();
    } catch (fallbackError) {
      console.error('Fallback loading also failed:', fallbackError);
      updateLoadingState(LoadingState.ERROR, {
        message: 'Unable to load questions. Please refresh the page and try again.'
      });

      // Show persistent error message
      setTimeout(() => {
        hideQuizLoadingOverlay();
        alert('Unable to load the assessment. Please refresh the page and try again.');
      }, 2000);
    }
  }
};

// Helper function to load a specific question batch
async function loadQuestionBatch(role, section, framework, email, batchNumber) {
  console.log(`Loading batch ${batchNumber} for ${role}/${section}`);

  try {
    const response = await fetch('/api/generate-quiz-batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role,
        section,
        framework,
        email,
        batchNumber,
        batchSize
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Server error (batch ${batchNumber}): ${errorData.error || response.statusText}`);
    }

    const batchData = await response.json();

    if (!Array.isArray(batchData.questions) || batchData.questions.length === 0) {
      throw new Error(`Invalid batch data received for batch ${batchNumber}`);
    }

    console.log(`Batch ${batchNumber} loaded successfully with ${batchData.questions.length} questions`);
    return batchData.questions;

  } catch (error) {
    console.error(`Error loading batch ${batchNumber}:`, error);
    throw error;
  }
}

// Helper function to load self-assessment questions
async function loadSelfAssessmentQuestions(role, section, framework, email) {
  console.log(`Loading self-assessment questions for ${role}/${section}`);

  try {
    const response = await fetch('/api/generate-self-assessment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role,
        section,
        framework,
        email
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Server error (self-assessment): ${errorData.error || response.statusText}`);
    }

    const selfAssessmentQuestions = await response.json();

    if (!Array.isArray(selfAssessmentQuestions) || selfAssessmentQuestions.length !== 5) {
      throw new Error('Invalid self-assessment question data received');
    }

    console.log('Self-assessment questions loaded successfully');
    return selfAssessmentQuestions;

  } catch (error) {
    console.error('Error loading self-assessment questions:', error);
    throw error;
  }
}

// Function to show skeleton loader for questions
function showQuestionSkeleton() {
  const questionElement = document.getElementById("question");
  const optionsContainer = document.getElementById("options-container");
  const skipBtn = document.getElementById("skip-btn");
  const messageElement = document.getElementById("message");

  if (questionElement) {
    questionElement.innerHTML = `
      <div class="question-skeleton">
        <div class="question-skeleton-title"></div>
        <div class="question-skeleton-text"></div>
        <div class="question-skeleton-text"></div>
        <div class="question-skeleton-text"></div>
        <div class="question-skeleton-options">
          <div class="question-skeleton-option"></div>
          <div class="question-skeleton-option"></div>
          <div class="question-skeleton-option"></div>
          <div class="question-skeleton-option"></div>
        </div>
        <div class="question-skeleton-skip"></div>
        <div class="loading-indicator">
          <span>Loading next questions</span>
          <div class="loading-dots">
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
          </div>
        </div>
      </div>
    `;
  }

  // Hide actual option buttons and skip button
  if (optionsContainer) {
    optionsContainer.style.display = 'none';
  }
  if (skipBtn) {
    skipBtn.style.display = 'none';
  }
  if (messageElement) {
    messageElement.innerHTML = '';
  }
}

// Function to hide skeleton loader and show actual question
function hideQuestionSkeleton() {
  const questionElement = document.getElementById("question");
  const optionsContainer = document.getElementById("options-container");
  const skipBtn = document.getElementById("skip-btn");

  if (questionElement) {
    questionElement.innerHTML = '';
  }
  if (optionsContainer) {
    optionsContainer.style.display = 'grid';
  }
  if (skipBtn) {
    skipBtn.style.display = 'block';
  }
}

// Function to show batch loading progress
function showBatchProgress(loadedBatches, totalBatches) {
  const messageElement = document.getElementById("message");
  if (messageElement) {
    const progressPercentage = Math.round((loadedBatches / totalBatches) * 100);
    messageElement.innerHTML = `
      <div class="batch-progress">
        <div>Loading questions: ${loadedBatches}/${totalBatches} batches complete</div>
        <div class="batch-progress-bar">
          <div class="batch-progress-fill" style="width: ${progressPercentage}%"></div>
        </div>
      </div>
    `;
  }
}

// Enhanced loadQuestion function that handles skeleton loading
const loadQuestionEnhanced = () => {
  // Check if we have questions available
  if (!quizData || quizData.length === 0) {
    showQuestionSkeleton();
    return;
  }

  // Check if current question index is beyond loaded questions
  if (currentQuestion >= quizData.length) {
    showQuestionSkeleton();
    showBatchProgress(loadedBatches.size, totalBatches);
    return;
  }

  // Hide skeleton and show actual question
  hideQuestionSkeleton();

  // Load the actual question
  const questionObj = quizData[currentQuestion];
  document.getElementById("question").innerText = questionObj.question;

  // Get the options container
  const optionsContainer = document.getElementById("options-container");

  // Apply question type specific styling
  if (questionObj.type === "self-assessment") {
    optionsContainer.classList.add("self-assessment");
    document.getElementById("message").innerHTML = '<span class="self-assessment-indicator">Self-Assessment Question</span>';
  } else {
    optionsContainer.classList.remove("self-assessment");
    document.getElementById("message").innerText = "";
  }

  // Handle different number of options (3 for self-assessment, 4 for knowledge check)
  const optionCount = questionObj.type === "self-assessment" ? 3 : 4;

  // Load options
  for (let i = 0; i < 4; i++) {
    const btn = document.getElementById(`btn${i}`);

    if (i < optionCount) {
      btn.innerText = questionObj.options[i];
      btn.className = "option-btn";
      btn.dataset.type = questionObj.type;
      btn.dataset.level = i + 1; // Store level for self-assessment (1=basic, 2=intermediate, 3=advanced)
      btn.disabled = false;
      btn.style.opacity = 1;
      btn.style.cursor = "pointer";
      btn.style.display = "block";
    } else {
      // Hide extra buttons if not needed (for self-assessment with 3 options)
      btn.style.display = "none";
    }
  }

  document.getElementById("skip-btn").disabled = false;
  document.getElementById("skip-btn").style.opacity = 1;
  document.getElementById("skip-btn").style.cursor = "pointer";
};

// Batch caching functions
function cacheBatchedQuestions(role, section, batchNumber, questions) {
  if (!window.questionBatchCache) {
    window.questionBatchCache = {};
  }

  const cacheKey = `${role}_${section}_batch_${batchNumber}`;
  window.questionBatchCache[cacheKey] = {
    questions: questions,
    timestamp: Date.now(),
    batchNumber: batchNumber
  };

  console.log(`Cached batch ${batchNumber} for ${role}/${section} with ${questions.length} questions`);
}

function getCachedBatchedQuestions(role, section, batchNumber) {
  if (!window.questionBatchCache) {
    return null;
  }

  const cacheKey = `${role}_${section}_batch_${batchNumber}`;
  const cached = window.questionBatchCache[cacheKey];

  if (cached) {
    const cacheAge = Date.now() - cached.timestamp;
    const CACHE_TTL = 30 * 60 * 1000; // 30 minutes

    if (cacheAge < CACHE_TTL) {
      console.log(`Using cached batch ${batchNumber} for ${role}/${section}`);
      return cached.questions;
    } else {
      console.log(`Cache expired for batch ${batchNumber}`);
      delete window.questionBatchCache[cacheKey];
    }
  }

  return null;
}

function getAllCachedBatches(role, section, totalBatches) {
  const allQuestions = [];
  let allBatchesCached = true;

  for (let i = 1; i <= totalBatches; i++) {
    const batchQuestions = getCachedBatchedQuestions(role, section, i);
    if (batchQuestions) {
      allQuestions.push(...batchQuestions);
    } else {
      allBatchesCached = false;
      break;
    }
  }

  return allBatchesCached ? allQuestions : null;
}

// Function to check if we need more questions for current position
function needsMoreQuestions() {
  if (!quizData || !isProgressiveLoading) {
    return false;
  }

  // Check if we're approaching the end of loaded questions
  const questionsRemaining = quizData.length - currentQuestion;
  const bufferSize = 2; // Load more when we have 2 or fewer questions remaining

  return questionsRemaining <= bufferSize && loadedBatches.size < totalBatches;
}

// Function to load additional batches when needed
async function loadAdditionalBatchesIfNeeded() {
  if (!needsMoreQuestions()) {
    return;
  }

  console.log('Loading additional batches as user approaches end of current questions');

  const role = document.getElementById("role").value.trim();
  const currentSectionName = sectionNames[currentSection - 1];
  const email = document.getElementById("email")?.value.trim() || null;

  // Get framework
  let framework = getCachedFramework(role);
  if (!framework) {
    const frameworkDoc = await db.collection('frameworks').doc(role).get();
    if (frameworkDoc.exists) {
      framework = frameworkDoc.data();
      cacheFramework(role, framework);
    }
  }

  // Load next batch
  const nextBatchNumber = loadedBatches.size + 1;
  if (nextBatchNumber <= totalBatches) {
    try {
      const nextBatch = await loadQuestionBatch(role, currentSectionName, framework, email, nextBatchNumber);

      // Add to quiz data
      const typedQuestions = nextBatch.map(q => ({
        ...q,
        type: "knowledge-check"
      }));

      quizData.push(...typedQuestions);
      batchedQuestions.push(...nextBatch);
      loadedBatches.add(nextBatchNumber);

      // Cache the batch
      cacheBatchedQuestions(role, currentSectionName, nextBatchNumber, nextBatch);

      console.log(`Loaded additional batch ${nextBatchNumber} with ${nextBatch.length} questions`);
    } catch (error) {
      console.error(`Error loading additional batch ${nextBatchNumber}:`, error);
    }
  }
}

// Main loadQuizData function - uses progressive loading by default
const loadQuizData = async () => {
  // Use progressive loading for better performance
  await loadQuizDataProgressive();
};

// Performance monitoring
const PerformanceTracker = {
  startTime: null,
  batchTimes: new Map(),
  metrics: {
    totalLoadTime: 0,
    firstBatchTime: 0,
    timeToFirstQuestion: 0,
    averageBatchTime: 0,
    cacheHitRate: 0,
    errorRate: 0
  },

  start() {
    this.startTime = performance.now();
    console.log('Performance tracking started');
  },

  markBatchStart(batchNumber) {
    this.batchTimes.set(`batch_${batchNumber}_start`, performance.now());
  },

  markBatchEnd(batchNumber) {
    const startTime = this.batchTimes.get(`batch_${batchNumber}_start`);
    if (startTime) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.batchTimes.set(`batch_${batchNumber}_duration`, duration);

      if (batchNumber === 1) {
        this.metrics.firstBatchTime = duration;
      }

      console.log(`Batch ${batchNumber} completed in ${duration.toFixed(2)}ms`);
    }
  },

  markFirstQuestionDisplayed() {
    if (this.startTime) {
      this.metrics.timeToFirstQuestion = performance.now() - this.startTime;
      console.log(`Time to first question: ${this.metrics.timeToFirstQuestion.toFixed(2)}ms`);
    }
  },

  calculateMetrics() {
    if (this.startTime) {
      this.metrics.totalLoadTime = performance.now() - this.startTime;
    }

    // Calculate average batch time
    const batchDurations = [];
    for (let [key, value] of this.batchTimes) {
      if (key.includes('_duration')) {
        batchDurations.push(value);
      }
    }

    if (batchDurations.length > 0) {
      this.metrics.averageBatchTime = batchDurations.reduce((a, b) => a + b, 0) / batchDurations.length;
    }

    // Calculate error rate
    this.metrics.errorRate = (loadingErrors.length / (totalBatches + 1)) * 100; // +1 for self-assessment

    console.log('Performance Metrics:', this.metrics);
    return this.metrics;
  },

  getOptimalBatchSize() {
    // Analyze performance to suggest optimal batch size
    const avgBatchTime = this.metrics.averageBatchTime;
    const firstBatchTime = this.metrics.firstBatchTime;

    if (firstBatchTime > 5000) { // If first batch takes more than 5 seconds
      return Math.max(2, batchSize - 1); // Reduce batch size
    } else if (firstBatchTime < 2000 && avgBatchTime < 3000) { // If batches are fast
      return Math.min(6, batchSize + 1); // Increase batch size
    }

    return batchSize; // Keep current size
  }
};

// Function to optimize batch size based on performance
function optimizeBatchSize() {
  const optimalSize = PerformanceTracker.getOptimalBatchSize();
  if (optimalSize !== batchSize) {
    console.log(`Optimizing batch size from ${batchSize} to ${optimalSize}`);
    batchSize = optimalSize;

    // Store in localStorage for future sessions
    try {
      localStorage.setItem('optimizedBatchSize', optimalSize.toString());
    } catch (e) {
      console.warn('Could not save optimized batch size to localStorage');
    }
  }
}

// Function to load optimized settings
function loadOptimizedSettings() {
  try {
    const savedBatchSize = localStorage.getItem('optimizedBatchSize');
    if (savedBatchSize) {
      const size = parseInt(savedBatchSize, 10);
      if (size >= 2 && size <= 6) {
        batchSize = size;
        console.log(`Using optimized batch size: ${batchSize}`);
      }
    }
  } catch (e) {
    console.warn('Could not load optimized settings from localStorage');
  }
}

// Initialize optimized settings
loadOptimizedSettings();

// Expose functions globally for use by other scripts
window.loadAdditionalBatchesIfNeeded = loadAdditionalBatchesIfNeeded;
window.needsMoreQuestions = needsMoreQuestions;
window.showQuestionSkeleton = showQuestionSkeleton;
window.hideQuestionSkeleton = hideQuestionSkeleton;
window.showBatchProgress = showBatchProgress;
window.loadQuestionEnhanced = loadQuestionEnhanced;
window.PerformanceTracker = PerformanceTracker;
window.optimizeBatchSize = optimizeBatchSize;
window.updateLoadingState = updateLoadingState;
window.LoadingState = LoadingState;

    // Original loadQuizData function (renamed for fallback)
const loadQuizDataOriginal = async () => {
  showQuizLoadingOverlay();

  let currentProgress = 0;
  updateLoadingProgress(currentProgress);

  const progressSteps = [
    { target: 20, duration: 1000 },
    { target: 40, duration: 2000 },
    { target: 60, duration: 2000 },
    { target: 80, duration: 2000 },
    { target: 90, duration: 1000 }
  ];

  let currentStep = 0;

  const animateProgress = (startValue, targetValue, duration) => {
    const startTime = Date.now();
    const animate = () => {
      const currentTime = Date.now();
      const elapsed = currentTime - startTime;

      if (elapsed < duration) {
        // Use easeInOutCubic for smoother animation
        const progress = elapsed / duration;
        const eased = progress < 0.5
          ? 4 * progress * progress * progress
          : 1 - Math.pow(-2 * progress + 2, 3) / 2;

        currentProgress = startValue + (targetValue - startValue) * eased;
        updateLoadingProgress(Math.round(currentProgress));
        requestAnimationFrame(animate);
      } else {
        currentProgress = targetValue;
        updateLoadingProgress(Math.round(currentProgress));
        if (currentStep < progressSteps.length - 1) {
          currentStep++;
          animateProgress(
            currentProgress,
            progressSteps[currentStep].target,
            progressSteps[currentStep].duration
          );
        }
      }
    };
    requestAnimationFrame(animate);
  };

  // Start the first animation
  animateProgress(0, progressSteps[0].target, progressSteps[0].duration);

  try {
    const role = document.getElementById("role").value.trim();
    const currentSectionName = sectionNames[currentSection - 1];

    showQuestionLoadingState();

    // Try to get the framework from our global cache first
    let framework = getCachedFramework(role);

    // If not in cache, try to get it from Firestore
    if (!framework) {
      console.log('Framework not found in cache, fetching from Firestore');
      const frameworkDoc = await db.collection('frameworks')
        .doc(role)
        .get();

      if (!frameworkDoc.exists) {
        throw new Error('Framework not found for role');
      }

      framework = frameworkDoc.data();

      // Cache it for future use
      cacheFramework(role, framework);
    } else {
      console.log('Using cached framework for role:', role);
    }

    // Get email for session tracking
    const email = document.getElementById("email")?.value.trim() || null;

    // Check if requests are already pending
    const quizParams = { role, section: currentSectionName };
    const selfAssessParams = { role, section: currentSectionName };

    if (isRequestPending('generate-quiz', quizParams) ||
        isRequestPending('generate-self-assessment', selfAssessParams)) {
      console.log('Requests already pending, waiting for completion...');

      // Instead of throwing an error, update the loading message
      const loadingMessage = document.getElementById('quiz-loading-message');
      if (loadingMessage) {
        loadingMessage.textContent = 'Questions are being processed. Please wait a moment...';
      }

      // Update progress indicator to show we're waiting
      currentProgress = 40; // Set to a medium progress value
      updateLoadingProgress(currentProgress);

      // Poll for completion of the pending requests
      let waitTime = 0;
      const pollInterval = 2000; // Check every 2 seconds
      const maxWaitTime = 45000; // 45 seconds maximum wait time

      return new Promise((resolve, reject) => {
        const checkCompletion = setInterval(() => {
          waitTime += pollInterval;

          if (!isRequestPending('generate-quiz', quizParams) &&
              !isRequestPending('generate-self-assessment', selfAssessParams)) {
            // Requests completed, try to get cached data
            clearInterval(checkCompletion);

            // Smoothly complete loading progress
            animateProgress(currentProgress, 100, 1000);

            setTimeout(() => {
              // Get the cached questions instead of making new API requests
              const cachedRegularQuestions = getCachedQuestions(role, currentSectionName);
              const cachedSelfAssessmentQuestions = getCachedSelfAssessmentQuestions(role, currentSectionName);

              if (cachedRegularQuestions && cachedSelfAssessmentQuestions) {
                console.log('Using cached questions from completed requests');

                // Mark regular questions with a type
                const typedRegularQuestions = cachedRegularQuestions.map(q => ({
                  ...q,
                  type: "knowledge-check"
                }));

                // Combine and shuffle the questions
                quizData = [...typedRegularQuestions, ...cachedSelfAssessmentQuestions];
                quizData = shuffleArray(quizData);

                questionsPerSection[currentSectionName] = quizData.length;
                sectionScores[currentSectionName] = 0;

                document.getElementById("current-section").innerText = currentSection;
                document.getElementById("section-name").innerText = currentSectionName;

                // Log the quiz data for tracking
                if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
                  window.quizLogger.logQuiz(quizData);
                }

                // Hide loading overlay
                hideQuizLoadingOverlay();

                // Load the question and update the progress bar
                loadQuestion();
                updateProgressBar();

                console.log('Quiz UI displayed successfully using cached data');
                resolve();
              } else {
                // If for some reason we don't have cached data, try again
                console.warn('Cached questions not found after request completion');
                hideQuizLoadingOverlay();
                loadQuizData().then(resolve).catch(reject);
              }
            }, 1200);

          } else if (waitTime >= maxWaitTime) {
            // Timeout reached, still pending
            clearInterval(checkCompletion);

            if (loadingMessage) {
              loadingMessage.textContent = 'Taking longer than expected. Retrying...';
            }

            // Show a message to the user
            showNotification('Questions are taking longer than expected to load. Retrying...', 'info');

            setTimeout(() => {
              hideQuizLoadingOverlay();
              loadQuizData().then(resolve).catch(reject);
            }, 1500);

          } else {
            // Still waiting, update progress
            currentProgress = Math.min(85, currentProgress + 3);
            updateLoadingProgress(currentProgress);

            if (loadingMessage && waitTime > 10000) { // After 10 seconds show countdown
              loadingMessage.textContent = `Still preparing questions (${Math.round((maxWaitTime - waitTime)/1000)}s remaining)...`;
            }
          }
        }, pollInterval);
      });
    }

    // Mark requests as pending
    const quizRequestKey = markRequestPending('generate-quiz', quizParams);
    const selfAssessRequestKey = markRequestPending('generate-self-assessment', selfAssessParams);

    // Update loading message to show we're fetching both types of questions
    const loadingMessage = document.getElementById('quiz-loading-message');
    if (loadingMessage) {
      loadingMessage.textContent = 'Preparing your personalized assessment...';
    }

    // Important: Update progress to indicate we're fetching questions
    animateProgress(currentProgress, 40, 1000);

    // Create state tracking variables for question loading
    let regularQuestionsReceived = false;
    let selfAssessmentQuestionsReceived = false;
    let regularQuestions = [];
    let selfAssessmentQuestions = [];

    console.log('Starting parallel fetch of both question types...');

    // Fetch both standard quiz questions and self-assessment questions in parallel
    // but handle each response separately to track when each type is received
    const regularQuestionsPromise = fetch('/api/generate-quiz', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role,
        section: currentSectionName,
        framework,
        email  // Include email as session identifier
      }),
    });

    const selfAssessmentPromise = fetch('/api/generate-self-assessment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        role,
        section: currentSectionName,
        framework,
        email  // Include email as session identifier
      }),
    });

    // Wait for both promises to resolve
    const [regularQuestionsResponse, selfAssessmentResponse] = await Promise.all([regularQuestionsPromise, selfAssessmentPromise]);

    // Update loading message to indicate we're now processing the responses
    if (loadingMessage) {
      loadingMessage.textContent = 'Received responses, processing questions...';
    }

    // Update progress to indicate we've received responses and are processing
    animateProgress(40, 60, 1000);

    // Process regular questions response
    if (!regularQuestionsResponse.ok) {
      const errorData = await regularQuestionsResponse.json().catch(() => ({}));
      throw new Error(`Server error (regular questions): ${errorData.error || regularQuestionsResponse.statusText}`);
    } else {
      console.log('Regular questions response received successfully');
      regularQuestions = await regularQuestionsResponse.json();
      regularQuestionsReceived = true;

      if (loadingMessage) {
        loadingMessage.textContent = 'Knowledge check questions received, waiting for self-assessment...';
      }

      // Validate regular questions
      if (!Array.isArray(regularQuestions) || regularQuestions.length !== 10) {
        throw new Error('Invalid regular question data received');
      }
      console.log('Regular questions validated successfully');

      // Cache the regular questions
      cacheQuestions(role, currentSectionName, 'regular', regularQuestions);
    }

    // Process self-assessment questions response
    if (!selfAssessmentResponse.ok) {
      const errorData = await selfAssessmentResponse.json().catch(() => ({}));
      throw new Error(`Server error (self-assessment): ${errorData.error || selfAssessmentResponse.statusText}`);
    } else {
      console.log('Self-assessment questions response received successfully');
      selfAssessmentQuestions = await selfAssessmentResponse.json();
      selfAssessmentQuestionsReceived = true;

      if (loadingMessage) {
        loadingMessage.textContent = 'Self-assessment questions received, preparing UI...';
      }

      // Validate self-assessment questions
      if (!Array.isArray(selfAssessmentQuestions) || selfAssessmentQuestions.length !== 5) {
        throw new Error('Invalid self-assessment question data received');
      }
      console.log('Self-assessment questions validated successfully');

      // Cache the self-assessment questions
      cacheQuestions(role, currentSectionName, 'self-assessment', selfAssessmentQuestions);
    }

    // Ensure both types of questions have been received before proceeding
    if (!regularQuestionsReceived || !selfAssessmentQuestionsReceived) {
      throw new Error('Failed to receive all required question types');
    }

    console.log('Both question types received and validated');

    // Update loading message to indicate we're preparing the UI
    if (loadingMessage) {
      loadingMessage.textContent = 'All questions received, preparing UI...';
    }

    // Update progress to indicate we're about to prepare the UI
    animateProgress(60, 80, 800);

    // Update progress to indicate we're preparing the UI
    animateProgress(80, 95, 500);

    // Mark regular questions with a type
    const typedRegularQuestions = regularQuestions.map(q => ({
      ...q,
      type: "knowledge-check"
    }));

    // Combine and shuffle the questions
    quizData = [...typedRegularQuestions, ...selfAssessmentQuestions];
    quizData = shuffleArray(quizData);

    questionsPerSection[currentSectionName] = quizData.length;
    sectionScores[currentSectionName] = 0;

    document.getElementById("current-section").innerText = currentSection;
    document.getElementById("section-name").innerText = currentSectionName;

    // Log the quiz data for tracking
    if (window.quizLogger && typeof window.quizLogger.logQuiz === 'function') {
      window.quizLogger.logQuiz(quizData);
    }

    // Final progress update before showing the UI
    animateProgress(95, 100, 300);

    console.log('All questions processed, preparing to display UI');
    console.log(`Total questions: ${quizData.length} (${typedRegularQuestions.length} knowledge check, ${selfAssessmentQuestions.length} self-assessment)`);

    // Only now, AFTER all data is prepared, do we display the first question
    // This ensures the UI only loads once with the complete set of questions
    setTimeout(() => {
      // Hide loading overlay first
      hideQuizLoadingOverlay();

      // Then load the question and update the progress bar
      loadQuestion();
      updateProgressBar();

      // Mark requests as completed
      markRequestCompleted(quizRequestKey);
      markRequestCompleted(selfAssessRequestKey);

      console.log('Quiz UI displayed successfully');
    }, 500);

  } catch (error) {
    console.error('Error loading quiz data:', error);

    // If we marked requests as pending but they failed, release them
    if (typeof quizRequestKey !== 'undefined') {
      markRequestCompleted(quizRequestKey);
    }
    if (typeof selfAssessRequestKey !== 'undefined') {
      markRequestCompleted(selfAssessRequestKey);
    }

    const errorMessage = error.message.includes('Network error')
      ? 'Connection error. Please check your internet connection and try again.'
      : error.message === 'Requests already in progress. Please wait.'
        ? 'Questions are already being loaded. Please wait.'
        : 'Failed to load quiz questions. Please try again.';

    showNotification(errorMessage, 'error');

    document.getElementById("question").innerText = "Error loading questions. Please try again.";
    document.querySelectorAll('.option-btn').forEach(btn => {
      btn.disabled = true;
      btn.innerText = '-';
    });

    // Still need to hide loading overlay even on error
    hideQuizLoadingOverlay();
  }
};

// Helper function to shuffle an array (Fisher-Yates algorithm)
function shuffleArray(array) {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}


      function showQuestionLoadingState() {
        document.getElementById("question").innerText = "Loading questions...";
        document.getElementById("progress-bar-fill").style.width = "0%";
        document.getElementById("progress-bar-text").innerText = "0%";

        const optionButtons = document.querySelectorAll('.option-btn');
        optionButtons.forEach(btn => {
          btn.disabled = true;
          btn.innerText = 'Loading...';
        });

        document.getElementById("skip-btn").disabled = true;
        document.getElementById("message").innerText = "";
      }

      // Use the enhanced loadQuestion function
      const loadQuestion = loadQuestionEnhanced;

        const restartQuiz = () => {
        currentQuestion = 0;
        score = 0;
        document.getElementById("score").innerText = "0";
        document.getElementById("failure-container").style.display = "none";
        document.getElementById("start-page").style.display = "block";
        document.getElementById("progress-bar-fill").style.width = "0%";
        document.getElementById("progress-bar-text").innerText = "0%";
        loadQuizData();
      };


    // Toast notification function from the framework
    function showToast(message, duration = 5000) {
        const toast = document.createElement('div');
        toast.classList.add('toast');
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, duration);
    }

    function updateProgressBar() {
      // Use a fixed total independent of when batches load
      const total = FIXED_TOTAL_QUESTIONS || (EXPECTED_KNOWLEDGE_TOTAL + EXPECTED_SELF_ASSESSMENT_TOTAL);

      // Displayed question number is 1-based for the UI
      const displayed = Math.min(Math.max(currentQuestion + 1, 1), total);
      const progress = (displayed / total) * 100;
      console.log('Progress:', { current: displayed, total, progress });

      // Update progress bar fill
      const progressBarFill = document.getElementById("progress-bar-fill");
      if (progressBarFill) progressBarFill.style.width = `${progress}%`;

      // Check or create text container once
      let progressBarText = document.getElementById("progress-bar-text");
      if (!progressBarText) {
        progressBarText = document.createElement("div");
        progressBarText.id = "progress-bar-text";
        progressBarText.className = "text-center text-gray-700 mb-2";
        const progressBar = document.getElementById("progress-bar");
        if (progressBar && progressBar.parentNode) {
          progressBar.parentNode.insertBefore(progressBarText, progressBar);
        }
      }

      // Update the progress text with fixed total
      if (progressBarText) {
        progressBarText.textContent = `Question ${displayed} of ${total} • ${Math.round(progress)}%`;
      }

      // If there are dedicated number slots, update them too
      const currentQuestionNumber = document.getElementById("current-question-number");
      if (currentQuestionNumber) currentQuestionNumber.innerText = `${displayed}`;
      const totalQuestionsDisplay = document.getElementById("total-questions");
      if (totalQuestionsDisplay) totalQuestionsDisplay.innerText = `${total}`;
    }

    global.startQuiz = startQuiz;
    global.endQuiz = endQuiz;
    global.loadQuizData = loadQuizData;
    global.loadQuestion = loadQuestion;
    global.updateProgressBar = updateProgressBar;
    global.restartQuiz = restartQuiz;
    global.logFinalResults = logFinalResults;
    window.showToast = showToast;
  })(typeof window !== 'undefined' ? window : global);
