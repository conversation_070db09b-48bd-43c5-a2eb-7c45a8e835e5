# AI Assessment Integration Guide for Frontend Developers

## Overview

This guide provides comprehensive documentation for integrating the AI assessment system alongside the existing digital skills assessment. The AI assessment system uses the same codebase but with separate database storage to ensure complete data isolation.

## Key Principles

1. **Data Separation**: AI assessments use `_ai` suffixed fields and collections
2. **Independent Operation**: Both assessment types can run simultaneously without interference
3. **Backward Compatibility**: Existing digital skills assessment functionality remains unchanged
4. **Assessment Type Parameter**: All API calls must include `assessmentType` parameter

## Database Schema

### User Document Fields

#### Digital Skills Assessment (Existing)
```javascript
{
  lastAssessmentId: string,
  lastAssessmentDate: Timestamp,
  skillsAnalysis: string,
  courseRecommendations: array,
  otherPathRecommendations: array,
  currentPath: string
}
```

#### AI Assessment (New - Parallel Structure)
```javascript
{
  lastAssessmentId_ai: string,
  lastAssessmentDate_ai: Timestamp,
  skillsAnalysis_ai: string,
  courseRecommendations_ai: array,
  otherPathRecommendations_ai: array,
  currentPath_ai: string
}
```

### Collections

#### Digital Skills Assessment
- `assessmentResults/` - Digital skills assessment results
- `assessmentSummaries/` - Digital skills assessment summaries

#### AI Assessment
- `assessmentResults_ai/` - AI assessment results
- `assessmentSummaries_ai/` - AI assessment summaries

## API Endpoints

### 1. Assessment Result Submission

**Endpoint**: `POST /api/assessment-result`

**Request Body**:
```javascript
{
  userEmail: string,
  role: string,
  userCompany: string,
  assessmentType: "ai_assessment", // NEW: Required for AI assessments
  assessmentResult: {
    learningPath: string,
    framework: object,
    userResponses: array
  }
}
```

**Response**:
```javascript
{
  success: boolean,
  message: string,
  analysis: {
    report: {
      competencyAnalysis: string,
      summary: string
    },
    recommendations: array,
    other_learning_paths_courses: array
  }
}
```

### 2. Question Generation

**Endpoint**: `POST /api/generate-quiz`

**Request Body**:
```javascript
{
  role: string,
  section: string,
  framework: object,
  email: string,
  assessmentType: "ai_assessment" // NEW: Required for AI assessments
}
```

**Response**:
```javascript
[
  {
    question: string,
    options: [string, string, string, string],
    answer: string,
    course: string,
    type: "knowledge-check"
  }
  // ... 9 more questions
]
```

### 3. Self-Assessment Generation

**Endpoint**: `POST /api/generate-self-assessment`

**Request Body**:
```javascript
{
  role: string,
  section: string,
  framework: object,
  email: string,
  assessmentType: "ai_assessment" // NEW: Required for AI assessments
}
```

**Response**:
```javascript
[
  {
    question: string,
    options: [string, string, string, string],
    skillArea: string,
    type: "self-assessment"
  }
  // ... 4 more questions
]
```

### 4. Batch Question Generation

**Endpoint**: `POST /api/generate-quiz-batch`

**Request Body**:
```javascript
{
  role: string,
  section: string,
  framework: object,
  email: string,
  batchNumber: number,
  batchSize: number,
  assessmentType: "ai_assessment" // NEW: Required for AI assessments
}
```

## Frontend Implementation Examples

### 1. Starting an AI Assessment

```javascript
// When user starts AI assessment, set assessment type
const assessmentType = 'ai_assessment';

// Store assessment type in session/local storage
sessionStorage.setItem('currentAssessmentType', assessmentType);

// Pass assessment type to all API calls
const generateQuestions = async (role, section, framework, email) => {
  const response = await fetch('/api/generate-quiz', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      role,
      section,
      framework,
      email,
      assessmentType: 'ai_assessment' // Critical: Include assessment type
    })
  });
  return response.json();
};
```

### 2. Submitting AI Assessment Results

```javascript
const submitAssessmentResults = async (userEmail, role, userCompany, assessmentResult) => {
  const response = await fetch('/api/assessment-result', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      userEmail,
      role,
      userCompany,
      assessmentType: 'ai_assessment', // Critical: Specify AI assessment
      assessmentResult
    })
  });
  return response.json();
};
```

### 3. Fetching User Assessment Data

```javascript
// Fetch AI assessment results separately from digital skills
const fetchAIAssessmentData = async (userRef) => {
  try {
    // Get latest AI assessment result
    const aiResultsSnapshot = await userRef
      .collection('assessmentResults_ai')
      .orderBy('timestamp', 'desc')
      .limit(1)
      .get();

    // Get latest AI assessment summary
    const aiSummarySnapshot = await userRef
      .collection('assessmentSummaries_ai')
      .orderBy('timestamp', 'desc')
      .limit(1)
      .get();

    const aiResult = !aiResultsSnapshot.empty ? aiResultsSnapshot.docs[0].data() : null;
    const aiSummary = !aiSummarySnapshot.empty ? aiSummarySnapshot.docs[0].data() : null;

    return {
      result: aiResult,
      summary: aiSummary,
      status: aiResult ? 'completed' : 'pending'
    };
  } catch (error) {
    console.error('Error fetching AI assessment data:', error);
    return null;
  }
};
```

## Data Isolation Best Practices

### 1. Assessment Type Validation

```javascript
// Always validate assessment type before API calls
const validateAssessmentType = (assessmentType) => {
  const validTypes = ['digital_skills', 'ai_assessment'];
  if (!validTypes.includes(assessmentType)) {
    throw new Error(`Invalid assessment type: ${assessmentType}`);
  }
  return assessmentType;
};
```

### 2. Separate Result Storage

```javascript
// Store results in appropriate collections based on assessment type
const storeAssessmentResult = async (userRef, resultData, assessmentType) => {
  const collectionName = assessmentType === 'ai_assessment' 
    ? 'assessmentResults_ai' 
    : 'assessmentResults';
    
  await userRef.collection(collectionName).add(resultData);
};
```

### 3. User Field Updates

```javascript
// Update user fields with appropriate suffixes
const updateUserFields = async (userRef, analysisResults, assessmentType) => {
  const fieldSuffix = assessmentType === 'ai_assessment' ? '_ai' : '';
  
  const updateData = {};
  updateData[`lastAssessmentId${fieldSuffix}`] = assessmentResultRef.id;
  updateData[`lastAssessmentDate${fieldSuffix}`] = admin.firestore.FieldValue.serverTimestamp();
  updateData[`skillsAnalysis${fieldSuffix}`] = analysisResults.report.summary;
  updateData[`courseRecommendations${fieldSuffix}`] = analysisResults.recommendations;
  
  await userRef.update(updateData);
};
```

## Error Handling

### 1. Assessment Type Errors

```javascript
try {
  const result = await submitAssessmentResults(/* ... */);
} catch (error) {
  if (error.message.includes('assessment type')) {
    console.error('Invalid assessment type specified');
    // Handle assessment type error
  }
}
```

### 2. Data Isolation Errors

```javascript
// Verify data separation
const verifyDataSeparation = async (userRef) => {
  const [digitalResults, aiResults] = await Promise.all([
    userRef.collection('assessmentResults').get(),
    userRef.collection('assessmentResults_ai').get()
  ]);
  
  console.log('Digital assessments:', digitalResults.size);
  console.log('AI assessments:', aiResults.size);
  
  // Both should be independent
  return {
    digitalCount: digitalResults.size,
    aiCount: aiResults.size,
    isolated: true // Both can exist independently
  };
};
```

## Testing Guidelines

### 1. Dual Assessment Testing

```javascript
// Test that both assessment types can coexist
const testDualAssessments = async (userEmail, company) => {
  // Complete digital skills assessment
  await submitAssessmentResults(userEmail, role, company, {
    assessmentType: 'digital_skills',
    // ... assessment data
  });
  
  // Complete AI assessment (should not interfere)
  await submitAssessmentResults(userEmail, role, company, {
    assessmentType: 'ai_assessment',
    // ... assessment data
  });
  
  // Verify both results exist independently
  const userData = await getUserData(userEmail, company);
  assert(userData.lastAssessmentId); // Digital skills
  assert(userData.lastAssessmentId_ai); // AI assessment
};
```

### 2. Cache Separation Testing

```javascript
// Verify caching doesn't interfere between assessment types
const testCacheSeparation = async () => {
  const role = 'Software Developer';
  const section = 'essentials';
  
  // Generate questions for both assessment types
  const digitalQuestions = await generateQuestions(role, section, framework, email, 'digital_skills');
  const aiQuestions = await generateQuestions(role, section, framework, email, 'ai_assessment');
  
  // Questions should be different (AI-focused vs digital skills-focused)
  assert(digitalQuestions !== aiQuestions);
};
```

## Migration Considerations

### 1. Existing Users

- Existing users will have only digital skills assessment data
- AI assessment fields will be null/undefined until first AI assessment
- No migration required - fields are added on-demand

### 2. Backward Compatibility

- All existing API calls without `assessmentType` default to `'digital_skills'`
- Existing frontend code continues to work unchanged
- New AI assessment features are additive

## Summary

The AI assessment system provides complete data isolation while maintaining the same user experience and API patterns. Key requirements for frontend integration:

1. **Always include `assessmentType: 'ai_assessment'` in API calls**
2. **Use separate data fetching for AI assessment results**
3. **Verify assessment type before processing results**
4. **Test both assessment types independently**

This approach ensures that users can take both digital skills and AI assessments without any data conflicts or interference.
